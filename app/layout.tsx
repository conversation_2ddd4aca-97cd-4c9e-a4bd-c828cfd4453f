import type React from 'react';
import type { <PERSON>adata } from 'next';
import { Analytics } from '@vercel/analytics/next';
import './globals.css';
import { Suspense } from 'react';
import CursorFollower from '@/components/cursor-follower';
import SmoothScroll from '@/components/smooth-scroll';
import Preloader from '@/components/preloader';

import { Cormoran<PERSON>_Garamond, Inter, Montserrat } from 'next/font/google';

const garamond = Cormorant_Garamond({
	subsets: ['latin'],
	weight: ['300', '400', '500', '600', '700'],
	variable: '--font-garamond',
	display: 'swap',
});

const montserrat = Montserrat({
	subsets: ['latin'],
	weight: ['300', '400', '500', '600', '700'],
	variable: '--font-gotham',
	display: 'swap',
});

export const metadata: Metadata = {
	title: 'sayosilver — African Heritage Meets Timeless Bridal Elegance',
	description:
		'Custom bridal gowns crafted for the woman who carries grace, strength, and legacy into her day of union.',
	generator: 'v0.app',
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang='en' className={`${gotham.variable} ${garamond.variable}`}>
			<body className='font-sans antialiased'>
				<Preloader />
				<SmoothScroll />
				<CursorFollower />
				<Suspense fallback={null}>{children}</Suspense>
				<Analytics />
			</body>
		</html>
	);
}
