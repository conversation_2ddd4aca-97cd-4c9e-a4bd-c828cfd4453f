"use client"

import { useEffect, useRef, useState } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import receptionDressesData from "@/data/reception-dresses.json"

gsap.registerPlugin(ScrollTrigger)

const ITEMS_PER_PAGE = 12

export default function ReceptionDressesPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const heroRef = useRef<HTMLDivElement>(null)
  const gridRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.from(".hero-title", {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      })

      gsap.from(".hero-description", {
        y: 50,
        opacity: 0,
        duration: 1,
        delay: 0.3,
        ease: "power3.out",
      })

      gsap.from(".item-card", {
        scrollTrigger: {
          trigger: gridRef.current,
          start: "top 80%",
        },
        y: 80,
        opacity: 0,
        duration: 0.8,
        stagger: 0.1,
        ease: "power3.out",
      })
    })

    return () => ctx.revert()
  }, [currentPage])

  const totalPages = Math.ceil(receptionDressesData.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const endIndex = startIndex + ITEMS_PER_PAGE
  const currentItems = receptionDressesData.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <section ref={heroRef} className="relative h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/elegant-african-reception-dress-with-modern-silhou.jpg"
            alt="Reception Dresses"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/40" />
        </div>

        <div className="relative z-10 text-center px-6 max-w-4xl">
          <h1 className="hero-title font-serif text-5xl md:text-7xl text-white mb-6">Reception Dresses</h1>
          <p className="hero-description text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
            Sophisticated ensembles designed for your celebration. Move freely, dance joyfully, and shine brilliantly.
          </p>
        </div>
      </section>

      <section ref={gridRef} className="py-24 lg:py-32 px-6">
        <div className="max-w-[1400px] mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {currentItems.map((item) => (
              <div key={item.id} className="item-card group">
                <div className="relative h-[600px] overflow-hidden mb-4">
                  <Image
                    src={item.image || "/placeholder.svg?height=600&width=400&query=elegant reception dress"}
                    alt={item.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <h3 className="font-serif text-2xl text-center group-hover:text-accent transition-colors">
                  {item.title}
                </h3>
              </div>
            ))}
          </div>

          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="disabled:opacity-50"
              >
                <ChevronLeft size={20} />
              </Button>

              <div className="flex gap-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="icon"
                    onClick={() => handlePageChange(page)}
                    className={currentPage === page ? "bg-foreground text-background" : ""}
                  >
                    {page}
                  </Button>
                ))}
              </div>

              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="disabled:opacity-50"
              >
                <ChevronRight size={20} />
              </Button>
            </div>
          )}
        </div>
      </section>

      <section className="py-24 px-6 bg-muted/30">
        <div className="max-w-[800px] mx-auto text-center">
          <h2 className="font-serif text-4xl md:text-5xl mb-6">Schedule Your Consultation</h2>
          <p className="text-lg text-foreground/70 mb-8 leading-relaxed">
            Experience these pieces in person and begin your bespoke bridal journey
          </p>
          <Button size="lg" className="bg-foreground text-background hover:bg-foreground/90 px-8">
            Book Appointment
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  )
}
