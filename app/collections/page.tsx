"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import Image from "next/image"
import Link from "next/link"
import collectionsData from "@/data/collections.json"

gsap.registerPlugin(ScrollTrigger)

export default function CollectionsPage() {
  const heroRef = useRef<HTMLDivElement>(null)
  const gridRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animation
      gsap.from(".collections-hero-title", {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      })

      gsap.from(".collections-hero-subtitle", {
        y: 50,
        opacity: 0,
        duration: 1,
        delay: 0.3,
        ease: "power3.out",
      })

      // Collection cards stagger
      gsap.from(".collection-card", {
        scrollTrigger: {
          trigger: gridRef.current,
          start: "top 80%",
        },
        y: 100,
        opacity: 0,
        duration: 1,
        stagger: 0.15,
        ease: "power3.out",
      })
    })

    return () => ctx.revert()
  }, [])

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Hero Section */}
      <section ref={heroRef} className="relative h-[70vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/luxury-bridal-collection-display-elegant-wedding-g.jpg"
            alt="Collections"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/40" />
        </div>

        <div className="relative z-10 text-center px-6 max-w-4xl">
          <h1 className="collections-hero-title font-serif text-5xl md:text-7xl lg:text-8xl text-white mb-6">
            Collections
          </h1>
          <p className="collections-hero-subtitle text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
            Seven unique collections, each named after remarkable Nigerian women, celebrating heritage and elegance
          </p>
        </div>
      </section>

      {/* Collections Grid - 3 columns */}
      <section ref={gridRef} className="py-24 lg:py-32 px-6">
        <div className="max-w-[1400px] mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {collectionsData.map((collection, index) => (
              <Link
                key={collection.id}
                href={`/collections/${collection.slug}`}
                className="collection-card group cursor-pointer"
              >
                <div className="relative h-[500px] overflow-hidden mb-6">
                  <Image
                    src={collection.image || "/placeholder.svg?height=500&width=400"}
                    alt={collection.name}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-500" />
                </div>

                <h3 className="font-serif text-2xl mb-3 group-hover:text-accent transition-colors">
                  {collection.name}
                </h3>
                <p className="text-foreground/70 leading-relaxed">{collection.description}</p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-6 bg-muted/30">
        <div className="max-w-[800px] mx-auto text-center">
          <h2 className="font-serif text-4xl md:text-5xl mb-6">Begin Your Journey</h2>
          <p className="text-lg text-foreground/70 mb-8 leading-relaxed">
            Schedule a private consultation to explore our collections and begin creating your dream bridal look
          </p>
        </div>
      </section>

      <Footer />
    </div>
  )
}
