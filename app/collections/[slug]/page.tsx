"use client"

import { useEffect, useRef, useState } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useParams } from "next/navigation"

import adaezeData from "@/data/adaeze.json"
import lolaData from "@/data/lola.json"
import zainabData from "@/data/zainab.json"
import chiomaData from "@/data/chioma.json"
import amaraData from "@/data/amara.json"
import ngoziData from "@/data/ngozi.json"
import funmiData from "@/data/funmi.json"
import collectionsData from "@/data/collections.json"

gsap.registerPlugin(ScrollTrigger)

const collectionDataMap: Record<string, any[]> = {
  adaeze: adaezeData,
  lola: lolaData,
  zainab: zainabData,
  chioma: chiomaData,
  amara: amaraData,
  ngozi: ngoziData,
  funmi: funmiData,
}

const ITEMS_PER_PAGE = 12

export default function CollectionPage() {
  const params = useParams()
  const slug = params.slug as string
  const [currentPage, setCurrentPage] = useState(1)

  const heroRef = useRef<HTMLDivElement>(null)
  const gridRef = useRef<HTMLDivElement>(null)

  const collection = collectionsData.find((c) => c.slug === slug)
  const items = collectionDataMap[slug] || []

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.from(".collection-hero-title", {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      })

      gsap.from(".collection-hero-description", {
        y: 50,
        opacity: 0,
        duration: 1,
        delay: 0.3,
        ease: "power3.out",
      })

      gsap.from(".item-card", {
        scrollTrigger: {
          trigger: gridRef.current,
          start: "top 80%",
        },
        y: 80,
        opacity: 0,
        duration: 0.8,
        stagger: 0.1,
        ease: "power3.out",
      })
    })

    return () => ctx.revert()
  }, [currentPage])

  if (!collection) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="font-serif text-4xl mb-4">Collection not found</h1>
          <p className="text-foreground/70">The collection you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  const totalPages = Math.ceil(items.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const endIndex = startIndex + ITEMS_PER_PAGE
  const currentItems = items.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Hero Section */}
      <section ref={heroRef} className="relative h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src={collection.image || "/placeholder.svg?height=800&width=1600"}
            alt={collection.name}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/40" />
        </div>

        <div className="relative z-10 text-center px-6 max-w-4xl">
          <h1 className="collection-hero-title font-serif text-5xl md:text-7xl text-white mb-6">{collection.name}</h1>
          <p className="collection-hero-description text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
            {collection.description}
          </p>
        </div>
      </section>

      {/* Items Grid */}
      <section ref={gridRef} className="py-24 lg:py-32 px-6">
        <div className="max-w-[1400px] mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {currentItems.map((item) => (
              <div key={item.id} className="item-card group">
                <div className="relative h-[600px] overflow-hidden mb-4">
                  <Image
                    src={item.image || "/placeholder.svg?height=600&width=400&query=luxury bridal gown"}
                    alt={item.name}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <h3 className="font-serif text-2xl text-center group-hover:text-accent transition-colors">
                  {item.name}
                </h3>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="disabled:opacity-50"
              >
                <ChevronLeft size={20} />
              </Button>

              <div className="flex gap-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="icon"
                    onClick={() => handlePageChange(page)}
                    className={currentPage === page ? "bg-foreground text-background" : ""}
                  >
                    {page}
                  </Button>
                ))}
              </div>

              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="disabled:opacity-50"
              >
                <ChevronRight size={20} />
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-6 bg-muted/30">
        <div className="max-w-[800px] mx-auto text-center">
          <h2 className="font-serif text-4xl md:text-5xl mb-6">Schedule Your Consultation</h2>
          <p className="text-lg text-foreground/70 mb-8 leading-relaxed">
            Experience these pieces in person and begin your bespoke bridal journey
          </p>
          <Button size="lg" className="bg-foreground text-background hover:bg-foreground/90 px-8">
            Book Appointment
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  )
}
