"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import Image from "next/image"
import { MagneticButton } from "@/components/magnetic-button"
import { Button } from "@/components/ui/button"

gsap.registerPlugin(ScrollTrigger)

export default function AboutPage() {
  const heroRef = useRef<HTMLDivElement>(null)
  const storyRef = useRef<HTMLDivElement>(null)
  const valuesRef = useRef<HTMLDivElement>(null)
  const founderRef = useRef<HTMLDivElement>(null)
  const craftRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animation
      gsap.from(".hero-title", {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      })

      gsap.from(".hero-subtitle", {
        y: 50,
        opacity: 0,
        duration: 1,
        delay: 0.3,
        ease: "power3.out",
      })

      // Story section
      gsap.from(".story-image", {
        scrollTrigger: {
          trigger: storyRef.current,
          start: "top 80%",
        },
        scale: 0.8,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      })

      gsap.from(".story-text", {
        scrollTrigger: {
          trigger: storyRef.current,
          start: "top 70%",
        },
        x: 100,
        opacity: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out",
      })

      // Values grid
      gsap.from(".value-card", {
        scrollTrigger: {
          trigger: valuesRef.current,
          start: "top 70%",
        },
        y: 80,
        opacity: 0,
        duration: 0.8,
        stagger: 0.15,
        ease: "power3.out",
      })

      // Founder section
      gsap.from(".founder-content", {
        scrollTrigger: {
          trigger: founderRef.current,
          start: "top 70%",
        },
        y: 60,
        opacity: 0,
        duration: 1,
        ease: "power3.out",
      })

      // Craft section parallax
      gsap.to(".craft-image", {
        scrollTrigger: {
          trigger: craftRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: 1,
        },
        y: -100,
      })
    })

    return () => ctx.revert()
  }, [])

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Hero Section */}
      <section ref={heroRef} className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image src="/luxury-african-bridal-atelier-workspace-with-elega.jpg" alt="Sayo Silver Atelier" fill className="object-cover" priority />
          <div className="absolute inset-0 bg-black/50" />
        </div>

        <div className="relative z-10 text-center px-6 max-w-4xl">
          <h1 className="hero-title font-serif text-5xl md:text-7xl lg:text-8xl text-white mb-6">Our Story</h1>
          <p className="hero-subtitle text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
            Where African heritage meets modern luxury in every stitch, every bead, every moment
          </p>
        </div>
      </section>

      {/* Story Section */}
      <section ref={storyRef} className="py-24 lg:py-32 px-6">
        <div className="max-w-[1400px] mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
            <div className="story-image relative h-[500px] lg:h-[700px]">
              <Image src="/african-bridal-designer-sketching-elegant-wedding-.jpg" alt="Our Heritage" fill className="object-cover" />
            </div>

            <div className="space-y-8">
              <div className="story-text">
                <h2 className="font-serif text-4xl md:text-5xl lg:text-6xl mb-6">
                  A Legacy of <span className="text-accent">Excellence</span>
                </h2>
              </div>

              <div className="story-text space-y-6 text-foreground/80 leading-relaxed">
                <p>
                  Sayo Silver was born from a vision to celebrate the rich tapestry of African heritage while embracing
                  the sophistication of contemporary bridal couture. Our journey began with a simple belief: every bride
                  deserves to feel extraordinary on her special day.
                </p>
                <p>
                  We draw inspiration from the vibrant cultures, intricate patterns, and timeless elegance that define
                  African artistry. Each gown we create is a masterpiece that tells a story—your story—woven with
                  threads of tradition and innovation.
                </p>
                <p>
                  Our atelier is more than a design studio; it's a sanctuary where dreams take shape, where every detail
                  is considered, and where the art of couture is honored with reverence and passion.
                </p>
              </div>

              <div className="story-text pt-4">
                <MagneticButton>
                  <Button size="lg" className="bg-foreground text-background hover:bg-foreground/90 px-8">
                    Explore Collections
                  </Button>
                </MagneticButton>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section ref={valuesRef} className="py-24 lg:py-32 px-6 bg-muted/30">
        <div className="max-w-[1400px] mx-auto">
          <div className="text-center mb-16">
            <h2 className="font-serif text-4xl md:text-5xl lg:text-6xl mb-6">Our Values</h2>
            <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
              The principles that guide every creation, every interaction, every moment
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="value-card bg-background p-8 lg:p-10">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6">
                <span className="text-2xl text-accent">✦</span>
              </div>
              <h3 className="font-serif text-2xl mb-4">Heritage</h3>
              <p className="text-foreground/70 leading-relaxed">
                We honor the rich traditions of African craftsmanship, weaving ancestral wisdom into every design with
                deep respect and authenticity.
              </p>
            </div>

            <div className="value-card bg-background p-8 lg:p-10">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6">
                <span className="text-2xl text-accent">✦</span>
              </div>
              <h3 className="font-serif text-2xl mb-4">Craftsmanship</h3>
              <p className="text-foreground/70 leading-relaxed">
                Every stitch, every bead, every detail is executed with meticulous precision by master artisans
                dedicated to perfection.
              </p>
            </div>

            <div className="value-card bg-background p-8 lg:p-10">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6">
                <span className="text-2xl text-accent">✦</span>
              </div>
              <h3 className="font-serif text-2xl mb-4">Innovation</h3>
              <p className="text-foreground/70 leading-relaxed">
                We push boundaries, exploring new techniques and materials while staying true to timeless elegance and
                sophistication.
              </p>
            </div>

            <div className="value-card bg-background p-8 lg:p-10">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6">
                <span className="text-2xl text-accent">✦</span>
              </div>
              <h3 className="font-serif text-2xl mb-4">Individuality</h3>
              <p className="text-foreground/70 leading-relaxed">
                Your story is unique, and so should be your gown. We celebrate your personal journey through bespoke
                design.
              </p>
            </div>

            <div className="value-card bg-background p-8 lg:p-10">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6">
                <span className="text-2xl text-accent">✦</span>
              </div>
              <h3 className="font-serif text-2xl mb-4">Sustainability</h3>
              <p className="text-foreground/70 leading-relaxed">
                We source responsibly, work ethically, and create timeless pieces that transcend fleeting trends and
                seasons.
              </p>
            </div>

            <div className="value-card bg-background p-8 lg:p-10">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6">
                <span className="text-2xl text-accent">✦</span>
              </div>
              <h3 className="font-serif text-2xl mb-4">Excellence</h3>
              <p className="text-foreground/70 leading-relaxed">
                We never compromise on quality, pursuing perfection in every aspect from initial sketch to final
                fitting.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Founder Section */}
      <section ref={founderRef} className="py-24 lg:py-32 px-6">
        <div className="max-w-[1200px] mx-auto">
          <div className="grid lg:grid-cols-5 gap-12 items-center">
            <div className="lg:col-span-2">
              <div className="founder-content relative h-[600px]">
                <Image src="/elegant-african-fashion-designer-portrait-in-luxur.jpg" alt="Founder" fill className="object-cover" />
              </div>
            </div>

            <div className="lg:col-span-3 founder-content space-y-6">
              <p className="text-sm tracking-widest text-accent uppercase">Founder & Creative Director</p>
              <h2 className="font-serif text-4xl md:text-5xl">Meet Sayo</h2>
              <div className="space-y-4 text-foreground/80 leading-relaxed">
                <p>
                  With over a decade of experience in haute couture, Sayo brings a unique perspective that bridges
                  continents and cultures. Trained in the finest ateliers of Paris and Lagos, she has cultivated a
                  distinctive aesthetic that honors her African roots while embracing global sophistication.
                </p>
                <p>
                  Her vision for Sayo Silver emerged from a desire to create bridal wear that celebrates the beauty and
                  diversity of African brides worldwide. Each collection reflects her commitment to excellence, her
                  passion for storytelling, and her belief that every bride deserves a gown as unique as her love story.
                </p>
                <p>
                  "I design for the woman who knows her worth, who celebrates her heritage, and who isn't afraid to make
                  a statement on the most important day of her life."
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Craft Section */}
      <section ref={craftRef} className="relative py-24 lg:py-32 overflow-hidden">
        <div className="craft-image absolute inset-0 z-0">
          <Image src="/luxury-bridal-atelier-with-artisans-working-on-wed.jpg" alt="Our Craft" fill className="object-cover" />
          <div className="absolute inset-0 bg-black/60" />
        </div>

        <div className="relative z-10 max-w-[1400px] mx-auto px-6 text-center text-white">
          <h2 className="font-serif text-4xl md:text-5xl lg:text-6xl mb-8">The Art of Couture</h2>
          <p className="text-lg md:text-xl max-w-3xl mx-auto leading-relaxed mb-12">
            Every Sayo Silver gown requires over 200 hours of meticulous handwork. From the initial sketch to the final
            fitting, our master artisans pour their expertise and passion into creating wearable art.
          </p>
          <MagneticButton>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-black bg-transparent px-8"
            >
              Book Your Consultation
            </Button>
          </MagneticButton>
        </div>
      </section>

      <Footer />
    </div>
  )
}
