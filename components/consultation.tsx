"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { Button } from "@/components/ui/button"

gsap.registerPlugin(ScrollTrigger)

export default function Consultation() {
  const sectionRef = useRef<HTMLElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLDivElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const textRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.to(imageRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: 1.5,
        },
        y: -100,
        scale: 1.1,
      })

      gsap.from(titleRef.current, {
        scrollTrigger: {
          trigger: contentRef.current,
          start: "top 80%",
          end: "top 50%",
          scrub: 1,
        },
        y: 100,
        opacity: 0,
        scale: 0.95,
      })

      gsap.from(textRef.current, {
        scrollTrigger: {
          trigger: contentRef.current,
          start: "top 75%",
          end: "top 45%",
          scrub: 1,
        },
        y: 80,
        opacity: 0,
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section ref={sectionRef} className="relative py-32 px-6 lg:px-12 overflow-hidden">
      <div ref={imageRef} className="absolute inset-0 w-full h-full">
        <div className="absolute inset-0 bg-black/60 z-10" />
        <img
          src="/luxury-bridal-consultation-elegant-setting.jpg"
          alt="Consultation"
          className="w-full h-full object-cover"
        />
      </div>

      <div ref={contentRef} className="relative z-20 max-w-3xl mx-auto text-center">
        <h2 ref={titleRef} className="font-serif text-4xl md:text-6xl text-white mb-6 text-balance">
          Begin With a Conversation
        </h2>
        <div ref={textRef}>
          <p className="text-lg text-white/90 mb-8 leading-relaxed text-pretty">
            Every masterpiece starts with dialogue. We invite you to book a private consultation—virtual or
            in-studio—where we'll discover your vision and begin shaping your dream gown.
          </p>
          <p className="text-sm tracking-wide text-white/70 mb-10 italic">By appointment only. Limited availability.</p>
          <Button size="lg" className="bg-white text-black hover:bg-white/90 px-12 h-14 text-base">
            Book Your Consultation
          </Button>
        </div>
      </div>
    </section>
  )
}
