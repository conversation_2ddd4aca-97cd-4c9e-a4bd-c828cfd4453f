'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

const categories = [
	{
		title: 'Reception Dresses',
		slug: 'reception-dresses',
		description:
			'For the dance, the laughter, the endless night. Our reception looks bring freedom of movement with the same touch of luxury.',
		image: '/elegant-african-reception-dress-with-modern-silhou.jpg',
		featured: true,
	},
	{
		title: 'Accessories',
		slug: 'accessories',
		description:
			'From veils to handcrafted headpieces, our accessories pay homage to African artistry while completing your bridal look.',
		image: '/luxury-bridal-accessories-headpiece-and-veil.jpg',
		featured: false,
	},
	{
		title: 'Bespoke Couture',
		slug: 'bespoke',
		description:
			'A completely personalized experience. Work directly with our designers to create a one-of-a-kind piece that tells your story.',
		image: '/luxury-bridal-atelier-with-artisans-working-on-wed.jpg',
		featured: false,
	},
];

export default function ReceptionAccessories() {
	const sectionRef = useRef<HTMLElement>(null);
	const titleRef = useRef<HTMLHeadingElement>(null);
	const cardsRef = useRef<(HTMLAnchorElement | null)[]>([]);

	useEffect(() => {
		const ctx = gsap.context(() => {
			gsap.from(titleRef.current, {
				scrollTrigger: {
					trigger: sectionRef.current,
					start: 'top 80%',
					end: 'top 50%',
					scrub: 1,
				},
				y: 80,
				opacity: 0,
			});

			cardsRef.current.forEach((card, index) => {
				if (card) {
					gsap.from(card, {
						scrollTrigger: {
							trigger: card,
							start: 'top 85%',
							end: 'top 55%',
							scrub: 1,
						},
						y: 100,
						opacity: 0,
						scale: 0.95,
					});
				}
			});
		}, sectionRef);

		return () => ctx.revert();
	}, []);

	return (
		<section ref={sectionRef} className='py-32 px-6 lg:px-12 bg-background'>
			<div className='max-w-[1400px] mx-auto'>
				<h2 ref={titleRef} className='font-serif text-4xl md:text-6xl text-center text-foreground mb-20 text-balance'>
					Complete Your Look
				</h2>

				{/* 2-column grid: left column full height, right column split top/bottom */}
				<div className='grid md:grid-cols-2 gap-6 lg:gap-8'>
					{categories.map((category, index) => (
						<Link
							key={category.slug}
							href={`/${category.slug}`}
							ref={el => {
								cardsRef.current[index] = el;
							}}
							className={`group relative overflow-hidden ${index === 0 ? 'md:row-span-2' : ''}`}>
							<div className={`relative overflow-hidden h-full ${index === 0 ? 'aspect-[3/4]' : 'aspect-[4/3]'}`}>
								<img
									src={category.image || '/placeholder.svg'}
									alt={category.title}
									className='size-full object-cover transition-transform duration-700 group-hover:scale-110'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent' />

								{/* Content overlay */}
								<div className='absolute inset-0 p-8 flex flex-col justify-end'>
									<h3 className='font-serif text-3xl md:text-4xl text-white mb-3'>{category.title}</h3>
									<p className='text-white/90 leading-relaxed mb-4 text-pretty max-w-md'>{category.description}</p>
									<span className='text-sm tracking-wide text-white flex items-center gap-2 group-hover:gap-3 transition-all'>
										Explore
										<ArrowRight className='size-4' />
									</span>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>
		</section>
	);
}
