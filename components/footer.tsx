export default function Footer() {
  const footerLinks = [
    { name: "About", href: "#about" },
    { name: "Collections", href: "#collections" },
    { name: "Bespoke Experience", href: "#bespoke" },
    { name: "Bride<PERSON>", href: "#brides" },
    { name: "Journal", href: "#journal" },
    { name: "Contact", href: "#contact" },
  ]

  const socialLinks = [
    { name: "Instagram", href: "#" },
    { name: "TikTok", href: "#" },
    { name: "Pinterest", href: "#" },
  ]

  return (
    <footer className="bg-foreground text-background py-20 px-6 lg:px-12">
      <div className="max-w-[1400px] mx-auto">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16">
          {/* Brand */}
          <div className="lg:col-span-2">
            <h3 className="font-serif text-3xl mb-4">sayosilver</h3>
            <p className="text-background/70 leading-relaxed mb-6 max-w-md">
              African heritage, custom luxury, and timeless bridal design.
            </p>
            <div className="space-y-3">
              <p className="text-sm tracking-wide text-background/80">
                Join the sayosilver circle for bridal inspiration
              </p>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Your email"
                  className="flex-1 px-4 py-2 bg-background/10 border border-background/20 text-background placeholder:text-background/50 focus:outline-none focus:border-background/40"
                />
                <button className="px-6 py-2 bg-background text-foreground hover:bg-background/90 transition-colors text-sm tracking-wide">
                  Subscribe
                </button>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-sm tracking-wide mb-4 text-background/60">Quick Links</h4>
            <ul className="space-y-2">
              {footerLinks.map((link) => (
                <li key={link.name}>
                  <a href={link.href} className="text-background/80 hover:text-background transition-colors text-sm">
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Social */}
          <div>
            <h4 className="text-sm tracking-wide mb-4 text-background/60">Follow Us</h4>
            <ul className="space-y-2">
              {socialLinks.map((link) => (
                <li key={link.name}>
                  <a href={link.href} className="text-background/80 hover:text-background transition-colors text-sm">
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="pt-8 border-t border-background/20 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-sm text-background/60">© {new Date().getFullYear()} sayosilver. All rights reserved.</p>
          <div className="flex gap-6 text-sm text-background/60">
            <a href="#" className="hover:text-background transition-colors">
              Privacy Policy
            </a>
            <a href="#" className="hover:text-background transition-colors">
              Terms of Service
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}
