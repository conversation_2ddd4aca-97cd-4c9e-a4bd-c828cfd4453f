"use client"

import { useEffect, useRef, useState } from "react"
import { gsap } from "gsap"
import Image from "next/image"

export default function Preloader() {
  const preloaderRef = useRef<HTMLDivElement>(null)
  const logoRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      const tl = gsap.timeline({
        onComplete: () => {
          setIsLoading(false)
        },
      })

      tl.to(logoRef.current, {
        scale: 0.8,
        opacity: 0,
        duration: 0.6,
        ease: "power2.in",
      }).to(preloaderRef.current, {
        y: "-100%",
        duration: 0.8,
        ease: "power4.inOut",
      })
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  if (!isLoading) return null

  return (
    <div ref={preloaderRef} className="fixed inset-0 z-[9999] bg-background flex items-center justify-center">
      <div ref={logoRef} className="relative w-48 h-24">
        <Image
          src="https://sayosilver.dovely.tech/wp-content/uploads/2025/10/sayo-silver-logo-light-theme.png"
          alt="Sayo Silver"
          fill
          className="object-contain"
          priority
        />
      </div>
    </div>
  )
}
