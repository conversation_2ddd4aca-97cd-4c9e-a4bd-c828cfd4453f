"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { ArrowRight } from "lucide-react"

gsap.registerPlugin(ScrollTrigger)

const articles = [
  {
    title: "The Art of African Bridal Couture",
    excerpt: "Exploring the rich traditions and modern interpretations that define our design philosophy.",
    image: "/luxury-african-bridal-wedding-gown-with-intricate-.jpg",
    date: "March 15, 2025",
    category: "Design",
  },
  {
    title: "Behind the Seams: A Day in the Atelier",
    excerpt: "Step inside our studio and witness the meticulous craftsmanship behind every gown.",
    image: "/luxury-bridal-atelier-with-artisans-working-on-wed.jpg",
    date: "March 8, 2025",
    category: "Process",
  },
  {
    title: "Choosing Your Perfect Bridal Silhouette",
    excerpt: "A guide to understanding which gown style celebrates your unique beauty.",
    image: "/african-bridal-designer-sketching-elegant-wedding-.jpg",
    date: "February 28, 2025",
    category: "Guide",
  },
]

export default function Journal() {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const cardsRef = useRef<(HTMLDivElement | null)[]>([])
  const imageRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.from(titleRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "top 50%",
          scrub: 1,
        },
        y: 100,
        opacity: 0,
        scale: 0.95,
      })

      cardsRef.current.forEach((card, index) => {
        if (card) {
          gsap.from(card, {
            scrollTrigger: {
              trigger: card,
              start: "top 90%",
              end: "top 60%",
              scrub: 1,
            },
            y: 120,
            opacity: 0,
            rotateX: 10,
          })

          const image = imageRefs.current[index]
          if (image) {
            gsap.to(image, {
              scrollTrigger: {
                trigger: card,
                start: "top bottom",
                end: "bottom top",
                scrub: 1.5,
              },
              y: -40,
              scale: 1.08,
            })
          }
        }
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section id="journal" ref={sectionRef} className="py-32 px-6 lg:px-12 bg-background">
      <div className="max-w-[1400px] mx-auto">
        <h2 ref={titleRef} className="font-serif text-4xl md:text-6xl text-center text-foreground mb-20 text-balance">
          The sayosilver Journal
        </h2>

        <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
          {articles.map((article, index) => (
            <div
              key={article.title}
              ref={(el) => {
                cardsRef.current[index] = el
              }}
              className="group cursor-pointer"
            >
              <div className="relative aspect-[4/5] overflow-hidden mb-6">
                <div
                  ref={(el) => {
                    imageRefs.current[index] = el
                  }}
                  className="w-full h-full"
                >
                  <img
                    src={article.image || "/placeholder.svg"}
                    alt={article.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-500" />
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 text-xs tracking-wide">
                  {article.category}
                </div>
              </div>
              <p className="text-xs tracking-wide text-foreground/60 mb-2">{article.date}</p>
              <h3 className="font-serif text-2xl text-foreground mb-3 group-hover:text-accent transition-colors">
                {article.title}
              </h3>
              <p className="text-foreground/70 leading-relaxed mb-4 text-pretty">{article.excerpt}</p>
              <button className="text-sm tracking-wide text-foreground flex items-center gap-2 group-hover:gap-3 transition-all">
                Read More
                <ArrowRight className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
