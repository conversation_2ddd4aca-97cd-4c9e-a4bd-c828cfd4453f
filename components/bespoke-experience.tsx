"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { Button } from "@/components/ui/button"

gsap.registerPlugin(ScrollTrigger)

const steps = [
  {
    number: "01",
    title: "Consultation",
    description: "Share your dream, and let us listen.",
    image: "/african-bridal-designer-sketching-elegant-wedding-.jpg",
  },
  {
    number: "02",
    title: "Design & Fabric Selection",
    description: "From sketch to texture, every detail is chosen with you in mind.",
    image: "/luxury-bridal-atelier-with-artisans-working-on-wed.jpg",
  },
  {
    number: "03",
    title: "Fittings",
    description: "Precision tailoring ensures your gown fits as though it was born with you.",
    image: "/luxury-custom-bridal-couture-fitting-session.jpg",
  },
  {
    number: "04",
    title: "Final Reveal",
    description: "A moment of majesty. A gown that is truly yours.",
    image: "/elegant-african-bride-in-luxury-white-wedding-gown.jpg",
  },
]

export default function BespokeExperience() {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const descRef = useRef<HTMLParagraphElement>(null)
  const stepsRef = useRef<(HTMLDivElement | null)[]>([])
  const imageRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    const ctx = gsap.context(() => {
      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "top top",
        end: "bottom bottom",
        pin: false,
      })

      gsap.from(titleRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "top 50%",
          scrub: 1,
        },
        y: 100,
        opacity: 0,
        scale: 0.95,
      })

      gsap.from(descRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 75%",
          end: "top 45%",
          scrub: 1,
        },
        y: 80,
        opacity: 0,
      })

      stepsRef.current.forEach((step, index) => {
        if (step) {
          const direction = index % 2 === 0 ? -100 : 100

          gsap.from(step, {
            scrollTrigger: {
              trigger: step,
              start: "top 85%",
              end: "top 55%",
              scrub: 1,
            },
            x: direction,
            opacity: 0,
            rotateY: index % 2 === 0 ? -5 : 5,
          })

          const image = imageRefs.current[index]
          if (image) {
            gsap.from(image, {
              scrollTrigger: {
                trigger: step,
                start: "top 85%",
                end: "top 55%",
                scrub: 1,
              },
              scale: 0.8,
              opacity: 0,
            })
          }
        }
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section id="bespoke" ref={sectionRef} className="py-32 px-6 lg:px-12 bg-background">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-20">
          <h2 ref={titleRef} className="font-serif text-4xl md:text-6xl text-foreground mb-6 text-balance">
            A Dress As Unique As You
          </h2>
          <p ref={descRef} className="text-lg text-foreground/80 max-w-3xl mx-auto leading-relaxed text-pretty">
            Your journey with sayosilver is intimate, deliberate, and deeply personal. We work with you to transform
            vision into reality, guiding you every step of the way.
          </p>
        </div>

        <div className="space-y-24">
          {steps.map((step, index) => (
            <div
              key={step.number}
              ref={(el) => {
                stepsRef.current[index] = el
              }}
              className={`flex flex-col ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"} gap-12 items-center`}
            >
              <div className="flex-1">
                <div
                  ref={(el) => {
                    imageRefs.current[index] = el
                  }}
                  className="relative aspect-[4/3] overflow-hidden"
                >
                  <img src={step.image || "/placeholder.svg"} alt={step.title} className="w-full h-full object-cover" />
                </div>
              </div>
              <div className="flex-1">
                <div className="flex items-start gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-20 h-20 border border-accent flex items-center justify-center">
                      <span className="font-serif text-2xl text-accent">{step.number}</span>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-serif text-3xl md:text-4xl text-foreground mb-4">{step.title}</h3>
                    <p className="text-lg text-foreground/70 leading-relaxed">{step.description}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-20">
          <Button size="lg" className="bg-foreground text-background hover:bg-foreground/90 px-8 h-12">
            Start Your Journey
          </Button>
        </div>
      </div>
    </section>
  )
}
