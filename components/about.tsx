"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"

gsap.registerPlugin(ScrollTrigger)

export default function About() {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLDivElement>(null)
  const paragraphsRef = useRef<(HTMLParagraphElement | null)[]>([])

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.from(titleRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "top 50%",
          scrub: 1,
        },
        y: 100,
        opacity: 0,
        scale: 0.9,
      })

      paragraphsRef.current.forEach((p, index) => {
        if (p) {
          gsap.from(p, {
            scrollTrigger: {
              trigger: p,
              start: "top 85%",
              end: "top 60%",
              scrub: 1,
            },
            y: 60,
            opacity: 0,
          })
        }
      })

      gsap.to(imageRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: 1.5,
        },
        y: -80,
        scale: 1.05,
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section id="about" ref={sectionRef} className="py-32 px-6 lg:px-12 bg-background">
      <div className="max-w-6xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="relative aspect-[4/5] overflow-hidden order-2 lg:order-1">
            <div ref={imageRef} className="w-full h-full">
              <img
                src="/luxury-african-bridal-atelier-workspace-with-elega.jpg"
                alt="sayosilver atelier"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          <div className="order-1 lg:order-2">
            <h2 ref={titleRef} className="font-serif text-4xl md:text-6xl text-foreground mb-8 text-balance">
              The Essence of sayosilver
            </h2>
            <div ref={contentRef} className="space-y-6 text-lg leading-relaxed text-foreground/80">
              <p
                ref={(el) => {
                  paragraphsRef.current[0] = el
                }}
                className="text-pretty"
              >
                At sayosilver, every gown is a story of heritage and artistry. We believe your wedding dress should not
                only fit your body—it should honor your journey, your culture, and your individuality.
              </p>
              <p
                ref={(el) => {
                  paragraphsRef.current[1] = el
                }}
                className="text-pretty"
              >
                Inspired by African traditions and shaped with modern couture craftsmanship, our designs embody
                intimacy, opulence, and identity.
              </p>
              <p
                ref={(el) => {
                  paragraphsRef.current[2] = el
                }}
                className="font-serif text-2xl text-foreground mt-8 italic"
              >
                This is not just a dress. This is your legacy, tailored to perfection.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
