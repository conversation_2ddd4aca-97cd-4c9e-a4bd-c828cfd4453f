"use client"

import { useState, useEffect } from "react"
import { Menu, X, ChevronDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import collectionsData from "@/data/collections.json"

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isMegaMenuOpen, setIsMegaMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled ? "bg-background/95 backdrop-blur-md border-b border-border" : "bg-transparent"
      }`}
    >
      <div className="max-w-[1400px] mx-auto px-6 lg:px-12">
        <div className="flex items-center justify-between h-20">
          <Link href="/" className="relative h-12 w-32">
            <Image
              src="https://sayosilver.dovely.tech/wp-content/uploads/2025/10/sayo-silver-logo-light-theme.png"
              alt="Sayo Silver"
              fill
              className={`object-contain transition-all duration-500 ${isScrolled ? "" : "invert brightness-0"}`}
              priority
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            <Link
              href="/about"
              className={`text-sm tracking-wide transition-colors ${
                isScrolled ? "text-foreground/70 hover:text-foreground" : "text-white/90 hover:text-white"
              }`}
            >
              About
            </Link>

            <div
              className="relative"
              onMouseEnter={() => setIsMegaMenuOpen(true)}
              onMouseLeave={() => setIsMegaMenuOpen(false)}
            >
              <button
                className={`flex items-center gap-1 text-sm tracking-wide transition-colors ${
                  isScrolled ? "text-foreground/70 hover:text-foreground" : "text-white/90 hover:text-white"
                }`}
              >
                Collections
                <ChevronDown size={16} className={`transition-transform ${isMegaMenuOpen ? "rotate-180" : ""}`} />
              </button>

              {/* Mega Menu Dropdown */}
              {isMegaMenuOpen && (
                <div className="absolute top-full left-1/2 -translate-x-1/2 pt-6 w-screen max-w-[1000px]">
                  <div className="bg-background border border-border shadow-2xl p-8">
                    <div className="grid grid-cols-3 gap-6">
                      {collectionsData.map((collection) => (
                        <Link
                          key={collection.id}
                          href={`/collections/${collection.slug}`}
                          className="group flex flex-col gap-3 p-4 hover:bg-muted/50 transition-colors"
                        >
                          <div className="relative w-full aspect-[3/4] overflow-hidden">
                            <Image
                              src={collection.image || "/placeholder.svg"}
                              alt={collection.name}
                              fill
                              className="object-cover group-hover:scale-110 transition-transform duration-500"
                            />
                          </div>
                          <div>
                            <h3 className="font-serif text-base mb-1 group-hover:text-accent transition-colors">
                              {collection.name}
                            </h3>
                            <p className="text-xs text-foreground/60 leading-relaxed">{collection.description}</p>
                          </div>
                        </Link>
                      ))}
                    </div>

                    <div className="mt-6 pt-6 border-t border-border text-center">
                      <Link
                        href="/collections"
                        className="text-sm tracking-wide text-accent hover:text-accent/80 transition-colors"
                      >
                        View All Collections →
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <Link
              href="/reception-dresses"
              className={`text-sm tracking-wide transition-colors ${
                isScrolled ? "text-foreground/70 hover:text-foreground" : "text-white/90 hover:text-white"
              }`}
            >
              Reception Dresses
            </Link>
            <Link
              href="/accessories"
              className={`text-sm tracking-wide transition-colors ${
                isScrolled ? "text-foreground/70 hover:text-foreground" : "text-white/90 hover:text-white"
              }`}
            >
              Accessories
            </Link>

            <a
              href="/#bespoke"
              className={`text-sm tracking-wide transition-colors ${
                isScrolled ? "text-foreground/70 hover:text-foreground" : "text-white/90 hover:text-white"
              }`}
            >
              Bespoke
            </a>
            <a
              href="/#brides"
              className={`text-sm tracking-wide transition-colors ${
                isScrolled ? "text-foreground/70 hover:text-foreground" : "text-white/90 hover:text-white"
              }`}
            >
              Brides
            </a>
            <a
              href="/#journal"
              className={`text-sm tracking-wide transition-colors ${
                isScrolled ? "text-foreground/70 hover:text-foreground" : "text-white/90 hover:text-white"
              }`}
            >
              Journal
            </a>
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <Button
              variant="outline"
              className={`transition-all ${
                isScrolled
                  ? "border-foreground text-foreground hover:bg-foreground hover:text-background bg-transparent"
                  : "border-white text-white hover:bg-white hover:text-black bg-transparent"
              }`}
            >
              Book Consultation
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className={`md:hidden transition-colors ${isScrolled ? "text-foreground" : "text-white"}`}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {isMobileMenuOpen && (
        <div className="md:hidden bg-background border-t border-border">
          <div className="px-6 py-6 space-y-4">
            <Link
              href="/about"
              className="block text-sm tracking-wide text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              About
            </Link>
            <Link
              href="/collections"
              className="block text-sm tracking-wide text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Collections
            </Link>
            <Link
              href="/reception-dresses"
              className="block text-sm tracking-wide text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Reception Dresses
            </Link>
            <Link
              href="/accessories"
              className="block text-sm tracking-wide text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Accessories
            </Link>
            <a
              href="/#bespoke"
              className="block text-sm tracking-wide text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Bespoke
            </a>
            <a
              href="/#brides"
              className="block text-sm tracking-wide text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Brides
            </a>
            <a
              href="/#journal"
              className="block text-sm tracking-wide text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Journal
            </a>
            <Button className="w-full bg-foreground text-background hover:bg-foreground/90">Book Consultation</Button>
          </div>
        </div>
      )}
    </nav>
  )
}
