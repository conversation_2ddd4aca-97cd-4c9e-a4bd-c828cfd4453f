"use client"

import { useEffect, useRef, useState } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

gsap.registerPlugin(ScrollTrigger)

export default function Hero() {
  const heroRef = useRef<HTMLDivElement>(null)
  const headlineRef = useRef<HTMLHeadingElement>(null)
  const sublineRef = useRef<HTMLParagraphElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)
  const videoRef = useRef<HTMLDivElement>(null)
  const video1Ref = useRef<HTMLVideoElement>(null)
  const video2Ref = useRef<HTMLVideoElement>(null)
  const [currentVideo, setCurrentVideo] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentVideo((prev) => (prev === 0 ? 1 : 0))
    }, 8000)

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    const ctx = gsap.context(() => {
      const headlineChars = headlineRef.current?.textContent?.split("") || []

      gsap.from(headlineRef.current, {
        y: 120,
        opacity: 0,
        duration: 1.4,
        ease: "power4.out",
        delay: 0.5,
      })

      gsap.from(sublineRef.current, {
        y: 80,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
        delay: 0.8,
      })

      gsap.from(ctaRef.current?.children || [], {
        y: 60,
        opacity: 0,
        duration: 1,
        stagger: 0.15,
        ease: "power3.out",
        delay: 1.1,
      })

      gsap.to(videoRef.current, {
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top top",
          end: "bottom top",
          scrub: 1.5,
        },
        y: 300,
        scale: 1.15,
        opacity: 0.3,
        ease: "none",
      })

      gsap.to(headlineRef.current, {
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top top",
          end: "bottom top",
          scrub: 1,
        },
        y: -150,
        opacity: 0,
        scale: 0.95,
        ease: "none",
      })

      gsap.to(sublineRef.current, {
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top top",
          end: "bottom top",
          scrub: 1,
        },
        y: -100,
        opacity: 0,
        ease: "none",
      })
    }, heroRef)

    return () => ctx.revert()
  }, [])

  return (
    <section ref={heroRef} className="relative h-screen flex items-center justify-center overflow-hidden">
      <div ref={videoRef} className="absolute inset-0 w-full h-full">
        <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-background z-10" />

        <video
          ref={video1Ref}
          autoPlay
          loop
          muted
          playsInline
          className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-2000 ${
            currentVideo === 0 ? "opacity-100" : "opacity-0"
          }`}
        >
          <source
            src="https://sayosilver.dovely.tech/wp-content/uploads/2025/10/african-bridal-video.mp4"
            type="video/mp4"
          />
        </video>

        <video
          ref={video2Ref}
          autoPlay
          loop
          muted
          playsInline
          className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-2000 ${
            currentVideo === 1 ? "opacity-100" : "opacity-0"
          }`}
        >
          <source
            src="https://sayosilver.dovely.tech/wp-content/uploads/2025/10/happy-bridal-her-video.mp4"
            type="video/mp4"
          />
        </video>
      </div>

      {/* Content */}
      <div className="relative z-20 max-w-5xl mx-auto px-6 text-center">
        <h1
          ref={headlineRef}
          className="font-serif text-5xl md:text-7xl lg:text-8xl text-white mb-6 leading-[1.1] text-balance"
        >
          Where African Heritage Meets Timeless Bridal Elegance
        </h1>
        <p
          ref={sublineRef}
          className="text-lg md:text-xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed text-pretty"
        >
          Custom gowns crafted for the woman who carries grace, strength, and legacy into her day of union.
        </p>
        <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-white text-black hover:bg-white/90 text-base px-8 h-12">
            Explore Collection
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="border-white text-white hover:bg-white hover:text-black text-base px-8 h-12 bg-transparent"
          >
            Book a Private Consultation
          </Button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex items-start justify-center p-2">
          <div className="w-1 h-3 bg-white/50 rounded-full animate-bounce" />
        </div>
      </div>
    </section>
  )
}
