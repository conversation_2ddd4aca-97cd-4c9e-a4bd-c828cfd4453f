"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"

gsap.registerPlugin(ScrollTrigger)

interface ImageRevealProps {
  src: string
  alt: string
  className?: string
}

export default function ImageReveal({ src, alt, className = "" }: ImageRevealProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: containerRef.current,
          start: "top 80%",
          end: "top 40%",
          scrub: 1,
        },
      })

      tl.to(overlayRef.current, {
        scaleX: 0,
        transformOrigin: "right",
        duration: 1,
        ease: "power3.inOut",
      }).from(
        imageRef.current,
        {
          scale: 1.3,
          duration: 1,
          ease: "power3.out",
        },
        0,
      )
    }, containerRef)

    return () => ctx.revert()
  }, [])

  return (
    <div ref={containerRef} className={`relative overflow-hidden ${className}`}>
      <img ref={imageRef} src={src || "/placeholder.svg"} alt={alt} className="w-full h-full object-cover" />
      <div ref={overlayRef} className="absolute inset-0 bg-background z-10" />
    </div>
  )
}
