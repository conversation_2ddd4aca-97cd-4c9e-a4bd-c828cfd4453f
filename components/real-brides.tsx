"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"

gsap.registerPlugin(ScrollTrigger)

const testimonials = [
  {
    quote: "The moment I wore my gown, I knew it wasn't just a dress. It was me—refined, radiant, unforgettable.",
    name: "Chiamaka",
    location: "Lagos",
    image: "/african-bride-in-elegant-wedding-gown-lagos.jpg",
  },
  {
    quote:
      "say<PERSON><PERSON><PERSON> understood my vision perfectly. Every detail honored my heritage while feeling completely modern.",
    name: "Amar<PERSON>",
    location: "Johannesburg",
    image: "/south-african-bride-luxury-wedding-dress.jpg",
  },
  {
    quote:
      "From consultation to final fitting, the experience was intimate and transformative. My gown was pure artistry.",
    name: "<PERSON><PERSON><PERSON>",
    location: "London",
    image: "/elegant-black-bride-london-wedding-gown.jpg",
  },
]

export default function RealBrides() {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const cardsRef = useRef<(HTMLDivElement | null)[]>([])
  const imageRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.from(titleRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "top 50%",
          scrub: 1,
        },
        y: 100,
        opacity: 0,
        scale: 0.95,
      })

      cardsRef.current.forEach((card, index) => {
        if (card) {
          gsap.from(card, {
            scrollTrigger: {
              trigger: card,
              start: "top 90%",
              end: "top 60%",
              scrub: 1,
            },
            y: 120,
            opacity: 0,
            rotateY: index % 2 === 0 ? -10 : 10,
          })

          const image = imageRefs.current[index]
          if (image) {
            gsap.to(image, {
              scrollTrigger: {
                trigger: card,
                start: "top bottom",
                end: "bottom top",
                scrub: 1.5,
              },
              scale: 1.1,
            })
          }
        }
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section id="brides" ref={sectionRef} className="py-32 px-6 lg:px-12 bg-muted">
      <div className="max-w-[1400px] mx-auto">
        <h2 ref={titleRef} className="font-serif text-4xl md:text-6xl text-center text-foreground mb-20 text-balance">
          Our Brides, Our Legacy
        </h2>

        <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.name}
              ref={(el) => {
                cardsRef.current[index] = el
              }}
              className="group"
            >
              <div className="relative aspect-[3/4] overflow-hidden mb-6">
                <div
                  ref={(el) => {
                    imageRefs.current[index] = el
                  }}
                  className="w-full h-full"
                >
                  <img
                    src={testimonial.image || "/placeholder.svg"}
                    alt={`${testimonial.name} from ${testimonial.location}`}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-500" />
              </div>
              <blockquote className="mb-4">
                <p className="font-serif text-xl text-foreground/90 leading-relaxed italic text-pretty">
                  "{testimonial.quote}"
                </p>
              </blockquote>
              <div className="text-sm tracking-wide">
                <p className="text-foreground font-medium">{testimonial.name}</p>
                <p className="text-foreground/60">{testimonial.location}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
