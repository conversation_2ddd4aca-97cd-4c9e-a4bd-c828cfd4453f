"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Link from "next/link"
import { ArrowRight } from "lucide-react"
import collectionsData from "@/data/collections.json"

gsap.registerPlugin(ScrollTrigger)

export default function Collections() {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const cardsRef = useRef<(HTMLDivElement | null)[]>([])
  const imageRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.from(titleRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "top 50%",
          scrub: 1,
        },
        y: 100,
        opacity: 0,
        scale: 0.9,
      })

      cardsRef.current.forEach((card, index) => {
        if (card) {
          gsap.from(card, {
            scrollTrigger: {
              trigger: card,
              start: "top 90%",
              end: "top 60%",
              scrub: 1,
            },
            y: 120,
            opacity: 0,
            rotateX: 15,
          })

          const image = imageRefs.current[index]
          if (image) {
            gsap.to(image, {
              scrollTrigger: {
                trigger: card,
                start: "top bottom",
                end: "bottom top",
                scrub: 1.5,
              },
              y: -50,
              scale: 1.1,
            })
          }
        }
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section id="collections" ref={sectionRef} className="py-32 px-6 lg:px-12 bg-muted">
      <div className="max-w-[1400px] mx-auto">
        <h2 ref={titleRef} className="font-serif text-4xl md:text-6xl text-center text-foreground mb-20 text-balance">
          Discover the Collections
        </h2>

        <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
          {collectionsData.map((collection, index) => (
            <Link
              key={collection.slug}
              href={`/collections/${collection.slug}`}
              ref={(el) => {
                cardsRef.current[index] = el
              }}
              className="group cursor-pointer block"
            >
              <div className="relative aspect-[3/4] overflow-hidden mb-6">
                <div
                  ref={(el) => {
                    imageRefs.current[index] = el
                  }}
                  className="w-full h-full"
                >
                  <img
                    src={collection.image || "/placeholder.svg"}
                    alt={collection.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-500" />
              </div>
              <h3 className="font-serif text-2xl md:text-3xl text-foreground mb-3">{collection.name}</h3>
              <p className="text-foreground/70 leading-relaxed mb-4 text-pretty">{collection.description}</p>
              <span className="text-sm tracking-wide text-foreground flex items-center gap-2 group-hover:gap-3 transition-all">
                View Collection
                <ArrowRight className="h-4 w-4" />
              </span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}
